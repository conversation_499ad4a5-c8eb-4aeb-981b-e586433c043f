import json
import os
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass

from agent_review.enhanced_retrieval_core import EnhancedRetrievalCore


@dataclass
class RetrievalResult:
    """检索结果数据类."""

    content: str
    message_id: str
    message_type: str
    score: float
    category: str
    start_message_id: int = None
    end_message_id: int = None


class SimpleRetrievalAggregator:
    """简化的检索聚合器，支持并行检索和结果聚合去重."""

    def __init__(self, knowledge_base: EnhancedRetrievalCore, config_path: str = None):
        """初始化检索聚合器.

        Args:
            knowledge_base: 增强版检索核心实例
            config_path: 配置文件路径，默认使用相对路径
        """
        self.knowledge_base = knowledge_base

        # 加载配置文件
        if config_path is None:
            config_path = os.path.join(
                os.path.dirname(__file__), "retrieval_queries.json"
            )

        with open(config_path, encoding="utf-8") as f:
            self.config = json.load(f)

        self.queries = self.config["retrieval_queries"]
        self.descriptions = self.config["category_descriptions"]
        self.search_config = self.config["search_config"]

    def _retrieve_for_category(
        self, category: str, queries: list[str]
    ) -> list[RetrievalResult]:
        """为单个类别执行检索.

        Args:
            category: 类别名称
            queries: 该类别的查询列表

        Returns:
            该类别的检索结果列表
        """
        category_results = []

        for query in queries:
            try:
                # 执行检索
                documents = self.knowledge_base.search(
                    query=query, top_k=self.search_config["top_k_per_category"]
                )

                # 转换为标准格式
                for doc in documents:
                    if doc.score >= self.search_config["score_threshold"]:
                        result = RetrievalResult(
                            content=doc.content,
                            message_id=str(doc.meta.get("message_id", "unknown")),
                            message_type=doc.meta.get("type", "unknown"),
                            score=doc.score,
                            category=category,
                            start_message_id=doc.meta.get("start_message_id"),
                            end_message_id=doc.meta.get("end_message_id"),
                        )
                        category_results.append(result)

            except Exception as e:
                print(f"检索失败 - 类别: {category}, 查询: {query}, 错误: {e}")
                continue

        return category_results

    def retrieve_all_categories(
        self, max_workers: int = 6
    ) -> dict[str, list[RetrievalResult]]:
        """并行检索所有类别.

        Args:
            max_workers: 最大并行工作线程数

        Returns:
            按类别分组的检索结果字典
        """
        results_by_category = {}

        # 使用线程池并行执行检索
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有检索任务
            future_to_category = {
                executor.submit(
                    self._retrieve_for_category, category, queries
                ): category
                for category, queries in self.queries.items()
            }

            # 收集结果
            for future in as_completed(future_to_category):
                category = future_to_category[future]
                try:
                    category_results = future.result()
                    results_by_category[category] = category_results
                    print(
                        f"✅ 类别 {category} 检索完成，找到 {len(category_results)} 个结果"
                    )
                except Exception as e:
                    print(f"❌ 类别 {category} 检索失败: {e}")
                    results_by_category[category] = []

        return results_by_category

    def aggregate_and_deduplicate(
        self, results_by_category: dict[str, list[RetrievalResult]]
    ) -> tuple[list[RetrievalResult], dict[str, set[str]]]:
        """聚合和去重检索结果.

        Args:
            results_by_category: 按类别分组的检索结果

        Returns:
            (去重后的结果列表, 每个类别对应的message_id集合)
        """
        # 用于去重的集合，基于content和message_id
        seen_content_ids = set()
        aggregated_results = []
        category_message_ids = defaultdict(set)

        # 收集所有结果并按message_id排序
        all_results = []
        for category, results in results_by_category.items():
            for result in results:
                all_results.append(result)
                category_message_ids[category].add(result.message_id)

        # 按message_id排序（尝试转换为int，失败则按字符串排序）
        def sort_key(result):
            try:
                # 处理可能的列表格式message_id
                if result.start_message_id is not None:
                    return result.start_message_id
                elif result.message_id.startswith("[") and result.message_id.endswith(
                    "]"
                ):
                    # 解析列表格式的message_id，取第一个
                    import ast

                    id_list = ast.literal_eval(result.message_id)
                    return int(id_list[0]) if id_list else 0
                else:
                    return int(result.message_id)
            except (ValueError, SyntaxError):
                return float("inf")  # 无法解析的放到最后

        all_results.sort(key=sort_key)

        # 去重：基于内容和message_id的组合
        for result in all_results:
            content_id_key = f"{result.content[:100]}_{result.message_id}"  # 使用内容前100字符+message_id作为去重key

            if content_id_key not in seen_content_ids:
                seen_content_ids.add(content_id_key)
                aggregated_results.append(result)

        # 限制总结果数量
        max_results = self.search_config["max_total_results"]
        if len(aggregated_results) > max_results:
            # 保持高分结果，按分数排序后取前N个
            aggregated_results.sort(key=lambda x: x.score, reverse=True)
            aggregated_results = aggregated_results[:max_results]
            # 重新按message_id排序
            aggregated_results.sort(key=sort_key)

        print("📊 聚合统计:")
        print(f"  - 原始结果总数: {len(all_results)}")
        print(f"  - 去重后结果数: {len(aggregated_results)}")
        for category, msg_ids in category_message_ids.items():
            print(f"  - {category}: {len(msg_ids)} 个不同的message_id")

        return aggregated_results, dict(category_message_ids)

    def format_results_for_llm(self, aggregated_results: list[RetrievalResult]) -> str:
        """将聚合结果格式化为LLM输入格式.

        Args:
            aggregated_results: 聚合后的检索结果

        Returns:
            格式化的字符串，用于LLM输入
        """
        if not aggregated_results:
            return "没有找到相关的检索结果。"

        formatted_parts = ["检索到的相关对话内容：\n"]

        for i, result in enumerate(aggregated_results):
            formatted_parts.append(f"文档 {i + 1}:")
            formatted_parts.append(f"  内容: {result.content}")
            formatted_parts.append(f"  消息ID: {result.message_id}")
            formatted_parts.append(f"  类型: {result.message_type}")
            formatted_parts.append(f"  相关类别: {result.category}")
            formatted_parts.append(f"  相关性分数: {result.score:.4f}")
            formatted_parts.append("")  # 空行分隔

        return "\n".join(formatted_parts)

    def retrieve_and_aggregate(
        self, max_workers: int = 6
    ) -> tuple[str, dict[str, set[str]], list[RetrievalResult]]:
        """执行完整的检索和聚合流程.

        Args:
            max_workers: 最大并行工作线程数

        Returns:
            (格式化的LLM输入字符串, 每个类别的message_id集合, 原始结果列表)
        """
        print("🔍 开始并行检索所有类别...")

        # 1. 并行检索所有类别
        results_by_category = self.retrieve_all_categories(max_workers)

        # 2. 聚合和去重
        print("\n📋 开始聚合和去重...")
        aggregated_results, category_message_ids = self.aggregate_and_deduplicate(
            results_by_category
        )

        # 3. 格式化为LLM输入
        print("\n📝 格式化结果用于LLM输入...")
        formatted_input = self.format_results_for_llm(aggregated_results)

        print(f"\n✅ 检索聚合完成，共 {len(aggregated_results)} 个有效结果")

        return formatted_input, category_message_ids, aggregated_results


if __name__ == "__main__":
    # 测试代码
    from agent_review.enhanced_retrieval_core import EnhancedRetrievalCore

    # 初始化知识库
    knowledge_base = EnhancedRetrievalCore()

    # 初始化聚合器
    aggregator = SimpleRetrievalAggregator(knowledge_base)

    # 执行检索和聚合
    formatted_input, category_message_ids, results = aggregator.retrieve_and_aggregate()

    print("\n" + "=" * 50)
    print("格式化的LLM输入:")
    print("=" * 50)
    print(formatted_input)

    print("\n" + "=" * 50)
    print("各类别的message_id:")
    print("=" * 50)
    for category, msg_ids in category_message_ids.items():
        print(f"{category}: {sorted(list(msg_ids))}")
