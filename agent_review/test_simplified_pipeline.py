#!/usr/bin/env python3
"""简化RAG管道测试脚本

这个脚本演示了如何使用新的简化RAG管道替代原有的Agent架构。
主要特点：
1. 一次检索 + 一次判断的简化流程
2. 并行检索提高性能
3. 统一LLM调用降低成本
4. 清晰的代码逻辑便于维护
"""

import time
from pprint import pprint

from agent_review.simplified_rag_pipeline import SimplifiedRAGPipeline


def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试1: 基本功能测试")
    print("="*50)
    
    # 初始化管道
    pipeline = SimplifiedRAGPipeline(
        min_content_length=4000,
        max_content_length=32000,
        merge_window=10,
        overlap_ratio=0.15,
    )
    
    print("✅ 管道初始化成功")
    print("📋 配置信息:", pipeline.get_insertion_stats())
    
    # 测试数据 - 包含公司信息询问
    caseId = "test_company_info"
    messages = [
        {"id": 1, "type": "USER", "msg": "你好"},
        {"id": 2, "type": "AGENT", "msg": "您好，欢迎使用KuCoin客服"},
        {"id": 3, "type": "USER", "msg": "我想了解一下你们公司的注册地址"},
        {"id": 4, "type": "AGENT", "msg": "好的，请稍等我为您查询"},
        {"id": 5, "type": "AGENT", "msg": "KuCoin的注册地址为塞舌尔共和国"},
        {"id": 6, "type": "USER", "msg": "谢谢"},
    ]
    
    # 插入数据
    print("\n📥 插入测试数据...")
    stats = pipeline.insert_data(caseId, messages, verbose=True)
    
    # 运行分析
    print(f"\n🔍 开始分析案例 {caseId}...")
    result = pipeline.run(caseId)
    
    print("\n📊 分析结果:")
    pprint(result)
    
    # 清理
    pipeline.clear_knowledge_base()
    print("\n✅ 测试1完成\n")
    
    return result


def test_multiple_categories():
    """测试多类别检测"""
    print("🧪 测试2: 多类别检测测试")
    print("="*50)
    
    pipeline = SimplifiedRAGPipeline()
    
    # 测试数据 - 包含多种敏感内容
    caseId = "test_multiple_categories"
    messages = [
        {"id": 1, "type": "USER", "msg": "你好"},
        {"id": 2, "type": "AGENT", "msg": "您好，我是客服小王"},
        {"id": 3, "type": "USER", "msg": "我听说KuCoin要跑路了，是真的吗？"},
        {"id": 4, "type": "AGENT", "msg": "这是谣言，请不要相信"},
        {"id": 5, "type": "USER", "msg": "那你们公司在哪里注册的？"},
        {"id": 6, "type": "AGENT", "msg": "我们在塞舌尔注册"},
        {"id": 7, "type": "AGENT", "msg": "能给我您的微信号吗？方便后续联系"},
        {"id": 8, "type": "USER", "msg": "为什么要我的微信？"},
    ]
    
    # 插入数据
    print("\n📥 插入测试数据...")
    pipeline.insert_data(caseId, messages, verbose=False)
    
    # 运行分析
    print(f"\n🔍 开始分析案例 {caseId}...")
    result = pipeline.run(caseId)
    
    print("\n📊 分析结果:")
    pprint(result)
    
    # 验证结果
    review_res = result.get("review_res", {})
    inquiry_count = len(review_res.get("sensitive_inquiry", []))
    reply_count = len(review_res.get("sensitive_reply", []))
    
    print("\n📈 结果验证:")
    print(f"  - 检测到敏感询问: {inquiry_count} 个")
    print(f"  - 检测到敏感回复: {reply_count} 个")
    
    # 清理
    pipeline.clear_knowledge_base()
    print("\n✅ 测试2完成\n")
    
    return result


def test_performance_comparison():
    """测试性能对比"""
    print("🧪 测试3: 性能测试")
    print("="*50)
    
    pipeline = SimplifiedRAGPipeline()
    
    # 生成较大的测试数据集
    caseId = "test_performance"
    messages = []
    
    # 生成100条消息
    for i in range(1, 101):
        if i % 2 == 1:  # 奇数为用户消息
            messages.append({
                "id": i,
                "type": "USER", 
                "msg": f"用户消息{i//2 + 1}: 这是一条测试消息，包含一些内容。"
            })
        else:  # 偶数为客服消息
            messages.append({
                "id": i,
                "type": "AGENT",
                "msg": f"客服回复{i//2}: 感谢您的咨询，我们会为您处理。"
            })
    
    # 在中间插入一些敏感内容
    messages[20]["msg"] = "请问你们公司的注册地址在哪里？"
    messages[21]["msg"] = "我们公司注册在塞舌尔"
    messages[50]["msg"] = "我听说你们平台不安全，是真的吗？"
    messages[51]["msg"] = "这是谣言，我们平台很安全"
    
    print(f"📊 测试数据: {len(messages)} 条消息")
    
    # 插入数据
    print("\n📥 插入数据...")
    start_time = time.time()
    pipeline.insert_data(caseId, messages, verbose=False)
    insert_time = time.time() - start_time
    print(f"✅ 数据插入完成，耗时: {insert_time:.2f}s")
    
    # 运行分析
    print("\n🔍 开始性能测试...")
    start_time = time.time()
    result = pipeline.run(caseId, max_workers=8)  # 使用8个并行工作线程
    total_time = time.time() - start_time
    
    print("\n⚡ 性能结果:")
    print(f"  - 总处理时间: {total_time:.2f}s")
    print(f"  - 平均每条消息: {total_time/len(messages)*1000:.1f}ms")
    print(f"  - 处理速度: {len(messages)/total_time:.1f} 消息/秒")
    
    # 清理
    pipeline.clear_knowledge_base()
    print("\n✅ 测试3完成\n")
    
    return {"total_time": total_time, "messages_count": len(messages)}


def test_edge_cases():
    """测试边界情况"""
    print("🧪 测试4: 边界情况测试")
    print("="*50)
    
    pipeline = SimplifiedRAGPipeline()
    
    # 测试空消息
    print("📝 测试空消息列表...")
    result1 = pipeline.run("empty_case")
    print("✅ 空消息处理正常")
    
    # 测试单条消息
    print("\n📝 测试单条消息...")
    pipeline.insert_data("single_case", [{"id": 1, "type": "USER", "msg": "hello"}])
    result2 = pipeline.run("single_case")
    print("✅ 单条消息处理正常")
    
    # 测试超长消息
    print("\n📝 测试超长消息...")
    long_message = "这是一条很长的消息。" * 1000  # 约10000字符
    pipeline.insert_data("long_case", [{"id": 1, "type": "USER", "msg": long_message}])
    result3 = pipeline.run("long_case")
    print("✅ 超长消息处理正常")
    
    # 清理
    pipeline.clear_knowledge_base()
    print("\n✅ 测试4完成\n")
    
    return [result1, result2, result3]


def main():
    """主测试函数"""
    print("🚀 简化RAG管道测试套件")
    print("="*60)
    print("这个测试套件将验证新的简化RAG管道的各项功能")
    print("="*60)
    
    try:
        # 运行所有测试
        test1_result = test_basic_functionality()
        test2_result = test_multiple_categories()
        test3_result = test_performance_comparison()
        test4_results = test_edge_cases()
        
        # 总结
        print("🎉 所有测试完成！")
        print("="*50)
        print("📊 测试总结:")
        print("  ✅ 基本功能测试: 通过")
        print("  ✅ 多类别检测测试: 通过")
        print(f"  ✅ 性能测试: {test3_result['total_time']:.2f}s ({test3_result['messages_count']} 消息)")
        print("  ✅ 边界情况测试: 通过")
        
        print("\n🎯 新系统优势:")
        print("  • 简化的架构，去除Agent复杂性")
        print("  • 并行检索，提高处理速度")
        print("  • 统一LLM调用，降低成本")
        print("  • 清晰的代码逻辑，便于维护")
        print("  • 与现有系统兼容的输出格式")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
