{"retrieval_queries": {"consulting_company_info": ["公司注册地址在哪里", "公司办公地址查询", "公司位置在中国新加坡香港马来西亚", "服务器位置在哪里", "员工在哪里工作", "客服代表位置在东南亚马来西亚", "为哪家公司服务", "公司是否有牌照许可证", "company registered address location", "office address inquiry", "server location where", "employee location based", "customer service representative location", "company license permit"], "selling_user_info": ["招聘客服人员做任务", "高额报酬奖励每天美元", "比特币加密货币支付", "切换到私人通讯渠道", "Telegram联系方式", "账户查找数据搜索", "提供修改账户信息", "出售用户数据信息", "recruitment customer support tasks", "high rewards daily payment", "bitcoin cryptocurrency payment", "switch private communication telegram", "account lookup data search", "provide modify account information", "sell user data information"], "negative_news": ["KuCoin跑路破产传言", "KuCoin被制裁监管", "领导员工被逮捕", "平台不安全数据泄露", "钱包密钥被盗", "大额资产转移", "域名被封锁", "储备金不足证明", "KuCoin running away bankruptcy rumors", "KuCoin sanctioned regulated", "leader employee arrested", "platform unsafe data breach", "wallet keys stolen", "large asset transfer", "domain blocked", "insufficient reserves proof"], "major_complaints": ["极度不满愤怒投诉", "威胁曝光公司", "社交媒体曝光", "律师法律诉讼", "报警举报", "监管机构投诉", "extremely dissatisfied angry complaint", "threaten expose company", "social media exposure", "lawyer legal action", "report to police", "regulatory complaint"], "request_contact_information": ["客服询问个人邮箱", "客服要求电话号码", "客服索要Telegram", "客服询问微信WeChat", "客服要求WhatsApp", "客服索要Twitter", "客服询问Facebook", "customer service ask personal email", "customer service request phone number", "customer service ask telegram", "customer service request wechat", "customer service ask whatsapp", "customer service request twitter", "customer service ask facebook"], "spam_messages": ["holy shit", "Jesus Christ", "Jesus fuck", "<PERSON>", "<PERSON>", "<PERSON> wept", "<PERSON>, <PERSON> and <PERSON>", "客服辱骂用户", "不当言论回复", "offensive language reply", "inappropriate response"]}, "category_descriptions": {"consulting_company_info": "查找用户询问公司信息的消息，如注册地址、办公位置、服务器位置、员工位置、许可证等", "selling_user_info": "查找涉及兜售用户信息、招聘内部人员、高额报酬、私人通讯渠道等可疑消息", "negative_news": "查找用户询问关于平台负面传言、安全问题、破产传言、监管问题等消息", "major_complaints": "查找用户表现出极度不满、威胁曝光、法律诉讼等重大投诉的消息", "request_contact_information": "查找客服主动询问用户个人联系方式的消息，如邮箱、电话、社交媒体等", "spam_messages": "查找客服回复中包含不当言论、辱骂性语言的消息"}, "search_config": {"top_k_per_category": 5, "score_threshold": 0.6, "max_total_results": 20}}