import time

from dotenv import load_dotenv

from agent_review.enhanced_retrieval_core import EnhancedRetrievalCore
from agent_review.simple_retrieval_aggregator import SimpleRetrievalAggregator
from agent_review.unified_llm_judge import UnifiedLLMJudge
from dc_ai_red_line_review.utils import get_logger

load_dotenv(override=True)


class SimplifiedRAGPipeline:
    """简化的RAG管道，替代Agent架构，使用'一次检索 + 一次判断'的方案"""
    
    def __init__(
        self,
        min_content_length: int = 50,
        max_content_length: int = 1000,
        merge_window: int = 3,
        overlap_ratio: float = 0.1,
        config_path: str = None,
    ):
        """初始化简化RAG管道
        
        Args:
            min_content_length: 最小内容长度，低于此长度的消息会被合并
            max_content_length: 最大内容长度，超过此长度的消息会被分块
            merge_window: 合并窗口大小，用于合并连续的短消息
            overlap_ratio: 分块时的重叠比例
            config_path: 检索查询配置文件路径
        """
        self.logger = get_logger(module_name="simplified_rag_pipeline")
        
        # 初始化知识库
        self.knowledge_base = EnhancedRetrievalCore(
            min_content_length=min_content_length,
            max_content_length=max_content_length,
            merge_window=merge_window,
            overlap_ratio=overlap_ratio,
        )
        
        # 初始化检索聚合器
        self.retrieval_aggregator = SimpleRetrievalAggregator(
            knowledge_base=self.knowledge_base,
            config_path=config_path
        )
        
        # 初始化LLM判断器
        self.llm_judge = UnifiedLLMJudge()
        
        self.logger.info("简化RAG管道初始化完成")
    
    def insert_data(self, caseId: str, messages: list[dict], verbose: bool = True) -> dict:
        """插入消息到知识库
        
        Args:
            caseId: 案例标识符
            messages: 消息列表，包含'msg'字段
            verbose: 是否打印详细信息
            
        Returns:
            插入统计信息字典
        """
        # 为每个消息添加caseId
        documents = [{**msg, "caseId": caseId} for msg in messages]
        
        # 使用增强版插入方法
        stats = self.knowledge_base.insert_documents_enhanced(
            documents=documents, content_key="msg"
        )
        
        if verbose:
            self.logger.info(f"📊 数据插入统计 (案例: {caseId}):")
            self.logger.info(f"  • 原始消息数量: {stats['total_input']}")
            self.logger.info(f"  • 处理后块数量: {stats['total_chunks']}")
            self.logger.info(f"  • 合并的块: {stats['merged_count']}")
            self.logger.info(f"  • 分割的块: {stats['split_count']}")
            self.logger.info(f"  • 单独的块: {stats['single_count']}")
            
            # 计算压缩比
            compression_ratio = (
                stats["total_chunks"] / stats["total_input"]
                if stats["total_input"] > 0
                else 0
            )
            self.logger.info(
                f"  • 压缩比: {compression_ratio:.2f} ({stats['total_chunks']}/{stats['total_input']})"
            )
            
            if stats["merged_count"] > 0:
                self.logger.info(f"  ✅ 成功合并 {stats['merged_count']} 个短对话块")
            if stats["split_count"] > 0:
                self.logger.info(f"  ✅ 成功分割 {stats['split_count']} 个长文本块")
        
        return stats
    
    def run(self, caseId: str = "", max_workers: int = 6) -> dict:
        """运行简化RAG管道进行敏感内容分析
        
        Args:
            caseId: 案例标识符
            max_workers: 并行检索的最大工作线程数
            
        Returns:
            分析结果字典，格式与现有系统兼容
        """
        start_time = time.time()
        self.logger.info(f"🚀 开始分析案例 {caseId}...")
        
        try:
            # 步骤1: 并行检索所有类别
            self.logger.info("📋 步骤1: 执行并行检索...")
            retrieval_start = time.time()
            
            formatted_input, category_message_ids, aggregated_results = (
                self.retrieval_aggregator.retrieve_and_aggregate(max_workers=max_workers)
            )
            
            retrieval_time = time.time() - retrieval_start
            self.logger.info(f"✅ 检索完成，耗时 {retrieval_time:.2f}s")
            
            # 步骤2: 统一LLM判断
            self.logger.info("🤖 步骤2: 执行统一LLM判断...")
            judge_start = time.time()
            
            if not aggregated_results:
                self.logger.info("⚠️ 没有检索到相关内容，返回空结果")
                result = self._get_empty_result()
            else:
                result = self.llm_judge.judge_all_categories(
                    retrieval_content=formatted_input,
                    category_message_ids=category_message_ids
                )
            
            judge_time = time.time() - judge_start
            self.logger.info(f"✅ LLM判断完成，耗时 {judge_time:.2f}s")
            
            # 步骤3: 格式化最终结果
            final_result = self._format_final_result(result, caseId)
            
            total_time = time.time() - start_time
            self.logger.info(f"🎉 案例 {caseId} 分析完成，总耗时 {total_time:.2f}s")
            self.logger.info(f"   - 检索耗时: {retrieval_time:.2f}s ({retrieval_time/total_time*100:.1f}%)")
            self.logger.info(f"   - 判断耗时: {judge_time:.2f}s ({judge_time/total_time*100:.1f}%)")
            
            return final_result
            
        except Exception as e:
            self.logger.error(f"❌ 案例 {caseId} 分析失败: {e}")
            return {"id": caseId, "review_res": self._get_empty_result(), "error": str(e)}
    
    def _get_empty_result(self) -> dict:
        """获取空的分析结果"""
        return {
            "sensitive_inquiry": [],
            "sensitive_reply": [],
        }
    
    def _format_final_result(self, analysis_result: dict, caseId: str) -> dict:
        """格式化最终结果，确保与现有系统兼容
        
        Args:
            analysis_result: LLM分析结果
            caseId: 案例标识符
            
        Returns:
            格式化的最终结果
        """
        # 确保结果格式正确
        formatted_result = {
            "id": caseId,
            "review_res": {
                "sensitive_inquiry": analysis_result.get("sensitive_inquiry", []),
                "sensitive_reply": analysis_result.get("sensitive_reply", []),
            }
        }
        
        # 统计结果
        inquiry_count = len(formatted_result["review_res"]["sensitive_inquiry"])
        reply_count = len(formatted_result["review_res"]["sensitive_reply"])
        
        # 统计命中的类别
        hit_categories = []
        for item in formatted_result["review_res"]["sensitive_inquiry"]:
            if item.get("hit_rule", False):
                hit_categories.append(item.get("type", "未知类别"))
        for item in formatted_result["review_res"]["sensitive_reply"]:
            if item.get("hit_rule", False):
                hit_categories.append(item.get("type", "未知类别"))
        
        self.logger.info("📊 分析结果统计:")
        self.logger.info(f"  - 敏感询问类别: {inquiry_count} 个")
        self.logger.info(f"  - 敏感回复类别: {reply_count} 个")
        if hit_categories:
            self.logger.info(f"  - 命中类别: {', '.join(hit_categories)}")
        else:
            self.logger.info("  - 未发现敏感内容")
        
        return formatted_result
    
    def clear_knowledge_base(self):
        """清空知识库"""
        self.knowledge_base.delete_all_documents()
        self.logger.info("🗑️ 知识库已清空")
    
    def get_insertion_stats(self) -> dict:
        """获取插入配置信息"""
        return {
            "min_content_length": self.knowledge_base.min_content_length,
            "max_content_length": self.knowledge_base.max_content_length,
            "merge_window": self.knowledge_base.merge_window,
            "overlap_ratio": self.knowledge_base.overlap_ratio,
        }


if __name__ == "__main__":
    # 使用示例
    pipeline = SimplifiedRAGPipeline(
        min_content_length=4000,
        max_content_length=32000,
        merge_window=10,
        overlap_ratio=0.15,
    )
    
    print("🚀 简化RAG管道初始化完成")
    print("📋 当前配置:", pipeline.get_insertion_stats())
    
    # 测试数据
    caseId = "simplified_test_001"
    messages = [
        {"id": 1, "type": "USER", "msg": "customer"},
        {"id": 2, "type": "USER", "msg": "customer support"},
        {"id": 3, "type": "USER", "msg": "support"},
        {"id": 4, "type": "USER", "msg": "KuCoin Pay Menu"},
        {"id": 5, "type": "AGENT", "msg": "Hi, it's Jacob here, thank you for contacting our customer support."},
        {"id": 6, "type": "AGENT", "msg": "I apologize for the inconvenience, Please hold on the line."},
        {"id": 7, "type": "USER", "msg": "Hi 我要報稅 機關那邊要我提供交易所主體所在地"},
        {"id": 8, "type": "AGENT", "msg": "您好，歡迎使用KuCoin在綫支持，我是Natalie，很高興爲您服務。"},
        {"id": 9, "type": "USER", "msg": "好的"},
        {"id": 10, "type": "AGENT", "msg": "您好，請問您是想咨詢KuCoin的注冊地址嗎？"},
        {"id": 11, "type": "USER", "msg": "是的"},
        {"id": 12, "type": "USER", "msg": "報稅機關要求我提供"},
        {"id": 13, "type": "AGENT", "msg": "好的請稍等"},
        {"id": 14, "type": "AGENT", "msg": "KuCoin的注册地址为 Vistra Corporate Services Centre, Suite 23, 1st Floor, Eden Plaza, Eden Island, Mahé , Republic of Seychelles"},
        {"id": 15, "type": "USER", "msg": "謝謝你"},
        {"id": 16, "type": "AGENT", "msg": "不客气"},
        {"id": 17, "type": "AGENT", "msg": "請問還有其他可以幫助您的嗎？"},
        {"id": 18, "type": "AGENT", "msg": "我發現你已經有一段時間沒有回應了，我將暫時結束此對話。"},
    ]
    
    # 插入数据
    stats = pipeline.insert_data(caseId, messages, verbose=True)
    
    # 运行分析
    result = pipeline.run(caseId)
    
    print("\n" + "="*50)
    print("分析结果:")
    print("="*50)
    import json
    print(json.dumps(result, ensure_ascii=False, indent=2))
